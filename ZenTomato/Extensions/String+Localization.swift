//
//  String+Localization.swift
//  ZenTomato
//
//  Created by Ban on 2025/8/16.
//  字符串本地化扩展 - 统一管理本地化字符串
//

import Foundation

// MARK: - String 本地化扩展

extension String {
    /// 获取本地化字符串
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /// 获取带参数的本地化字符串
    func localized(with arguments: CVarArg...) -> String {
        return String(format: NSLocalizedString(self, comment: ""), arguments: arguments)
    }
}

// MARK: - 本地化管理器

/// 本地化管理器 - 提供统一的本地化字符串访问
struct LocalizationManager {
    
    // MARK: - 通知相关
    
    struct Notification {
        static let breakStartTitle = "notification.break_start.title".localized
        static let breakEndTitle = "notification.break_end.title".localized
        static let workStartTitle = "notification.work_start.title".localized
        
        static let breakEndBody = "notification.break_end.body".localized
        static let workStartBody = "notification.work_start.body".localized
        
        /// 获取休息开始通知内容
        static func breakStartBody(minutes: Int) -> String {
            return "notification.break_start.body".localized(with: minutes)
        }
        
        // 通知动作
        static let skipAction = "notification.action.skip".localized
        static let startNowAction = "notification.action.start_now".localized
    }
    
    // MARK: - 权限相关
    
    struct Permission {
        static let notificationTitle = "permission.notification.title".localized
        static let requestMessage = "permission.notification.request_message".localized
        static let deniedMessage = "permission.notification.denied_message".localized
        static let requestButton = "permission.notification.button.request".localized
        static let settingsButton = "permission.notification.button.settings".localized
    }
    
    // MARK: - 状态显示
    
    struct Status {
        static let notDetermined = "status.not_determined".localized
        static let denied = "status.denied".localized
        static let authorized = "status.authorized".localized
        static let provisional = "status.provisional".localized
        static let ephemeral = "status.ephemeral".localized
        static let unknown = "status.unknown".localized
    }
    
    // MARK: - 界面文字
    
    struct Settings {
        static let notificationTitle = "settings.notification.title".localized
        static let enabledMessage = "settings.notification.enabled_message".localized
        static let description = "settings.notification.description".localized
    }
    
    // MARK: - 错误信息
    
    struct Error {
        static let permissionFailed = "error.notification.permission_failed".localized
        static let sendFailed = "error.notification.send_failed".localized
        static let unauthorized = "error.notification.unauthorized".localized
    }
}

// MARK: - 预览辅助

#if DEBUG
extension LocalizationManager {
    /// 预览用的本地化字符串
    static var preview: LocalizationManager {
        return LocalizationManager()
    }
}
#endif
