/* 
   Localizable.strings (Chinese Simplified)
   ZenTomato
   
   Created by Ban on 2025/8/16.
   中文本地化字符串
*/

// MARK: - 通知相关
"notification.break_start.title" = "休息时间到了！";
"notification.break_start.body" = "休息 %d 分钟，让眼睛和身体放松一下";
"notification.break_end.title" = "休息结束！";
"notification.break_end.body" = "准备好继续专注工作了吗？";
"notification.work_start.title" = "开始工作！";
"notification.work_start.body" = "保持专注，全力以赴";
"notification.welcome.title" = "通知已启用！";
"notification.welcome.body" = "禅番茄将在阶段切换时提醒您";

// MARK: - 通知动作
"notification.action.skip" = "跳过休息";
"notification.action.start_now" = "立即开始";

// MARK: - 权限相关
"permission.notification.title" = "通知权限";
"permission.notification.request_message" = "禅番茄需要通知权限来在阶段切换时提醒您";
"permission.notification.denied_message" = "通知权限被拒绝，请在系统设置中启用";
"permission.notification.button.request" = "请求权限";
"permission.notification.button.settings" = "打开设置";

// MARK: - 状态显示
"status.not_determined" = "未决定";
"status.denied" = "已拒绝";
"status.authorized" = "已授权";
"status.provisional" = "临时授权";
"status.ephemeral" = "临时授权";
"status.unknown" = "未知";

// MARK: - 界面文字
"settings.notification.title" = "通知设置";
"settings.notification.enabled_message" = "通知已启用，将在阶段切换时提醒您";
"settings.notification.description" = "接收工作和休息阶段的提醒通知";

// MARK: - 错误信息
"error.notification.permission_failed" = "通知权限请求失败";
"error.notification.send_failed" = "发送通知失败";
"error.notification.unauthorized" = "通知未授权，无法发送通知";
