/* 
   Localizable.strings (English)
   ZenTomato
   
   Created by Ban on 2025/8/16.
   English localization strings
*/

// MARK: - Notifications
"notification.break_start.title" = "Time for a Break!";
"notification.break_start.body" = "Take a %d-minute break to rest your eyes and body";
"notification.break_end.title" = "Break Over!";
"notification.break_end.body" = "Ready to get back to focused work?";
"notification.work_start.title" = "Let's Work!";
"notification.work_start.body" = "Stay focused and give it your all";
"notification.welcome.title" = "Notifications Enabled!";
"notification.welcome.body" = "ZenTomato will remind you when phases change";

// MARK: - Notification Actions
"notification.action.skip" = "Skip Break";
"notification.action.start_now" = "Start Now";

// MARK: - Permissions
"permission.notification.title" = "Notification Permission";
"permission.notification.request_message" = "ZenTomato needs notification permission to remind you when phases change";
"permission.notification.denied_message" = "Notification permission denied, please enable it in System Settings";
"permission.notification.button.request" = "Request Permission";
"permission.notification.button.settings" = "Open Settings";

// MARK: - Status Display
"status.not_determined" = "Not Determined";
"status.denied" = "Denied";
"status.authorized" = "Authorized";
"status.provisional" = "Provisional";
"status.ephemeral" = "Ephemeral";
"status.unknown" = "Unknown";

// MARK: - Interface Text
"settings.notification.title" = "Notification Settings";
"settings.notification.enabled_message" = "Notifications enabled, you'll be reminded when phases change";
"settings.notification.description" = "Receive reminder notifications for work and break phases";

// MARK: - Error Messages
"error.notification.permission_failed" = "Notification permission request failed";
"error.notification.send_failed" = "Failed to send notification";
"error.notification.unauthorized" = "Notifications not authorized, cannot send notification";
