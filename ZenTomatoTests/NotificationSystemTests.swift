//
//  NotificationSystemTests.swift
//  ZenTomatoTests
//
//  Created by Ban on 2025/8/16.
//  通知系统测试 - 验证通知功能的完整性
//

import XCTest
import UserNotifications
@testable import ZenTomato

/// 通知系统测试类
class NotificationSystemTests: XCTestCase {
    
    var notificationManager: NotificationManager!
    
    override func setUp() {
        super.setUp()
        notificationManager = NotificationManager()
    }
    
    override func tearDown() {
        notificationManager = nil
        super.tearDown()
    }
    
    /// 测试通知权限请求
    func testNotificationPermissionRequest() {
        let expectation = XCTestExpectation(description: "Permission request completed")
        
        // 请求权限
        notificationManager.requestPermission()
        
        // 等待权限状态更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 验证权限状态已更新
            XCTAssertNotEqual(self.notificationManager.authorizationStatus, .notDetermined)
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    /// 测试本地化字符串
    func testNotificationLocalization() {
        // 测试中文本地化
        let chineseTitle = NSLocalizedString("notification.break_start.title", comment: "")
        XCTAssertFalse(chineseTitle.isEmpty, "本地化字符串不应为空")
        
        let chineseBody = String(format: NSLocalizedString("notification.break_start.body", comment: ""), 5)
        XCTAssertTrue(chineseBody.contains("5"), "本地化字符串应包含格式化参数")
        
        // 测试动作本地化
        let skipAction = NSLocalizedString("notification.action.skip", comment: "")
        let startAction = NSLocalizedString("notification.action.start_now", comment: "")
        
        XCTAssertFalse(skipAction.isEmpty, "跳过动作本地化不应为空")
        XCTAssertFalse(startAction.isEmpty, "开始动作本地化不应为空")
    }
    
    /// 测试通知内容创建
    func testNotificationContentCreation() {
        // 模拟已授权状态
        notificationManager.isAuthorized = true
        
        // 测试休息开始通知
        let breakDuration: TimeInterval = 5 * 60 // 5分钟
        notificationManager.sendBreakStartNotification(duration: breakDuration)
        
        // 测试休息结束通知
        notificationManager.sendBreakEndNotification()
        
        // 测试工作开始通知
        notificationManager.sendWorkStartNotification()
        
        // 验证没有崩溃即为成功
        XCTAssertTrue(true, "通知发送方法应正常执行")
    }
    
    /// 测试权限状态显示
    func testAuthorizationStatusDisplay() {
        let statuses: [UNAuthorizationStatus] = [
            .notDetermined, .denied, .authorized, .provisional
        ]
        
        for status in statuses {
            let displayName = status.displayName
            let icon = status.icon
            let color = status.color
            
            XCTAssertFalse(displayName.isEmpty, "状态显示名称不应为空")
            XCTAssertFalse(icon.isEmpty, "状态图标不应为空")
            XCTAssertNotNil(color, "状态颜色不应为nil")
        }
    }
    
    /// 测试系统设置URL
    func testSystemSettingsURL() {
        // 测试打开系统设置不会崩溃
        notificationManager.openSystemSettings()
        XCTAssertTrue(true, "打开系统设置应正常执行")
    }
    
    /// 测试通知清理功能
    func testNotificationCleanup() {
        // 测试清理方法不会崩溃
        notificationManager.cleanupOldNotifications()
        notificationManager.removeAllPendingNotifications()
        notificationManager.removeAllDeliveredNotifications()
        
        XCTAssertTrue(true, "通知清理方法应正常执行")
    }
}
